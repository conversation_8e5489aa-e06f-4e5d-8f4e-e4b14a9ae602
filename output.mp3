X-RequestId:45a60c94e3e742cbb4d516f7df3ab7f6
Content-Type:application/json; charset=utf-8
Path:audio.metadata

{
  "Metadata": [
    {
      "Type": "WordBoundary",
      "Data": {
        "Offset": 1250000,
        "Duration": 2000000,
        "text": {
          "Text": "Xin",
          "Length": 3,
          "BoundaryType": "WordBoundary"
        }
      }
    }
  ]
}X-RequestId:45a60c94e3e742cbb4d516f7df3ab7f6
Content-Type:application/json; charset=utf-8
Path:audio.metadata

{
  "Metadata": [
    {
      "Type": "WordBoundary",
      "Data": {
        "Offset": 3250000,
        "Duration": 2625000,
        "text": {
          "Text": "chÃ o",
          "Length": 4,
          "BoundaryType": "WordBoundary"
        }
      }
    }
  ]
}X-RequestId:45a60c94e3e742cbb4d516f7df3ab7f6
Content-Type:application/json; charset=utf-8
Path:audio.metadata

{
  "Metadata": [
    {
      "Type": "SentenceBoundary",
      "Data": {
        "Offset": 1000000,
        "Duration": 18875000,
        "text": {
          "Text": "Xin chÃ o tháº¿ giá»i!",
          "Length": 18,
          "BoundaryType": "SentenceBoundary"
        }
      }
    }
  ]
}X-RequestId:45a60c94e3e742cbb4d516f7df3ab7f6
Content-Type:application/json; charset=utf-8
Path:audio.metadata

{
  "Metadata": [
    {
      "Type": "WordBoundary",
      "Data": {
        "Offset": 5875000,
        "Duration": 2250000,
        "text": {
          "Text": "tháº¿",
          "Length": 3,
          "BoundaryType": "WordBoundary"
        }
      }
    }
  ]
}X-RequestId:45a60c94e3e742cbb4d516f7df3ab7f6
Content-Type:application/json; charset=utf-8
Path:audio.metadata

{
  "Metadata": [
    {
      "Type": "WordBoundary",
      "Data": {
        "Offset": 8125000,
        "Duration": 3375000,
        "text": {
          "Text": "giá»i",
          "Length": 4,
          "BoundaryType": "WordBoundary"
        }
      }
    }
  ]
}