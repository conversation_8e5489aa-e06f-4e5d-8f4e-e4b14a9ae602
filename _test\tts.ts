
import { EdgeTTS } from '../src/edge-tts/index'

async function main() {
  console.log('Testing EdgeTTS class...')

  // Test class instantiation
  const tts = new EdgeTTS();
  console.log('✓ EdgeTTS instance created')

  // Test voice setting
  tts.setVoice('en-US-AriaNeural')
  console.log('✓ Voice set to en-US-AriaNeural')

  // Test list voices (this should work in Node.js)
  try {
    console.log('Fetching available voices...')
    const voices = await EdgeTTS.listVoices()
    console.log(`✓ Found ${voices.length} voices`)
    console.log('First 3 voices:', voices.slice(0, 3).map(v => v.ShortName))
  } catch (error) {
    console.log('✗ Error fetching voices:', error.message)
  }

  console.log('\nNote: synthesize() requires Cloudflare Workers environment (WebSocket support)')
}

main()