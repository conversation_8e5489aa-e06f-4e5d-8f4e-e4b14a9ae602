import fs from 'fs';

import { EdgeTTS } from '../src/edge-tts/tts-ws'

async function main() {
  console.log('Testing EdgeTTS class...')

  // Test class instantiation
  const tts = new EdgeTTS();
  console.log('✓ EdgeTTS instance created')

  // Test voice setting
  tts.setVoice('en-US-AndrewMultilingualNeural')
  console.log('✓ Voice set to en-US-AndrewMultilingualNeural')

  // Test list voices (this should work in Node.js)
  try {
    console.log('Fetching available voices...')
    const voices = await EdgeTTS.listVoices()
    console.log(`✓ Found ${voices.length} voices`)
    console.log('First 3 voices:', voices.slice(0, 3).map(v => v.ShortName))
  } catch (error) {
    console.log('✗ Error fetching voices:', error.message)
  }

  // Test synthesize (this should work in Cloudflare Workers environment)
  try {
    console.log('Synthesizing text...')
    const result = await tts.synthesize('Xin chào thế giới!')
    console.log('✓ Synthesis successful')
    console.log('Audio chunks:', result.chunks.length)
    console.log('Word subtitle:', result.wordSubtitle.generate_subs())
    console.log('Sentence subtitle:', result.sentenceSubtitle.generate_subs())
    // save audio to file with fs
    const fs = require('fs')
    const blob = new Blob([...result.chunks], { type: 'audio/mpeg' })
    const buffer = Buffer.from(await blob.arrayBuffer())
    fs.writeFileSync('output.mp3', buffer)
  } catch (error) {
    console.log('✗ Error synthesizing text:', error.message)
  }
}

// main()

async function main2() {
  const tts = new EdgeTTS();
  tts.setVoice('vi-VN-HoaiMyNeural'); // Vietnamese voice
  
  console.log('Synthesizing text...');
  const result = await tts.synthesize('Xin chào thế giới!');
  console.log('✓ Synthesis successful');
  console.log('Audio chunks:', result.chunks);
  
  if (result.chunks.length === 0) {
    console.error('❌ No audio data received!');
    return;
  }
  
  // Calculate total size
  const totalSize = result.chunks.reduce((sum, chunk) => sum + chunk.length, 0);
  console.log(`📊 Total audio data: ${totalSize} bytes`);
  const blob = new Blob([...result.chunks], { type: 'audio/mpeg' })
  // Combine all chunks into one buffer
  const combinedBuffer = Array.from(new Uint8Array(await blob.arrayBuffer()), x => String.fromCharCode(x)).join('')
  
  // Save to file
  const outputFile = 'output.mp3';
  fs.writeFileSync(outputFile, combinedBuffer);
  console.log(`💾 Audio saved to ${outputFile} (${combinedBuffer.length} bytes)`);
  
  // Print subtitles
  console.log('\n📝 Word subtitle:');
  console.log(result.wordSubtitle.generate_subs());
  
  console.log('\n📝 Sentence subtitle:');
  console.log(result.sentenceSubtitle.generate_subs());
}

main2().catch(console.error);