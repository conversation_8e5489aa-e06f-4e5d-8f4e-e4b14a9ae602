{"name": "tool-cf-app", "version": "0.0.0", "devDependencies": {"@cloudflare/workers-types": "^4.20240529.0", "@types/lodash": "^4.17.5", "esbuild": "^0.17.8", "esbuild-plugin-solid": "^0.4.2", "esbuild-plugin-tailwindcss": "^1.1.0", "esbuild-sass-plugin": "^2.4.5", "npm-run-all": "^4.1.5", "sass": "^1.58.3", "solid-js": "^1.6.11", "tailwindcss": "^3.2.7", "typescript": "^4.9.5", "wrangler": "3.57.2"}, "private": true, "scripts": {"start": "run-p start:*", "start:wrangler": "wrangler dev", "start:html": "node ./build-frontend.mjs --dev", "deploy": "node ./build-frontend.mjs && wrangler deploy"}, "dependencies": {"@tailwindcss/typography": "^0.5.13", "bulma": "^0.9.4", "github-markdown-css": "^5.5.1", "hono": "^4.3.6", "lodash": "^4.17.21", "marked": "^12.0.2", "ws": "^8.18.3"}}