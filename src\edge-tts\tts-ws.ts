import WebSocket from 'ws';
import { OUTPUT_FORMAT } from "./constants";
import { SubMaker } from "./SubMaker";

function randomUUID(): string {
  // Generate a random UUIDv4 string
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });

  return uuid;
}

type Connection = {
  ws: WebSocket
}

export interface VoiceInfo {
  Name: string;
  ShortName: string;
  Gender: string;
  Locale: string;
  SuggestedCodec: string;
  FriendlyName: string;
  Status: string;
  VoiceTag: {
    ContentCategories: any[];
    VoicePersonalities: any[];
  };
}

export class EdgeTTS {
  private static TRUSTED_CLIENT_TOKEN = "6A5AA1D4EAFF4E9FB37E23D68491D6F4";
  private static VOICES_URL = `https://speech.platform.bing.com/consumer/speech/synthesize/readaloud/voices/list?trustedclienttoken=${EdgeTTS.TRUSTED_CLIENT_TOKEN}`;
  private static SYNTH_URL = `wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=${EdgeTTS.TRUSTED_CLIENT_TOKEN}`;

  format = OUTPUT_FORMAT.AUDIO_24KHZ_48KBITRATE_MONO_MP3
  voice = 'zh-CN-YunxiaNeural'
  voiceLocale = 'zh-CN';

  setOutputFormat(format: OUTPUT_FORMAT): this {
    this.format = format;
    return this;
  }

  setVoice(voice: string): this {
    this.voice = voice;
    this.voiceLocale = /^\w+-\w+/.exec(voice)![0]
    return this;
  }

  static async listVoices(): Promise<VoiceInfo[]> {
    const response = await fetch(EdgeTTS.VOICES_URL);
    return await response.json();
  }

  private _wsPromise: Promise<Connection> | undefined
  private getConnection(): Promise<Connection> {
    if (this._wsPromise) return this._wsPromise

    const promise = new Promise<Connection>((resolve, reject) => {
      const wsUrl = `${EdgeTTS.SYNTH_URL}&ConnectionId=${connect_id()}`;
      
      const ws = new WebSocket(wsUrl, {
        headers: {
          "Pragma": "no-cache",
          "Cache-Control": "no-cache",
          "Origin": "chrome-extension://jdiccldimpdaibmpdkjnbmckianbfold",
          "Accept-Encoding": "gzip, deflate, br",
          "Accept-Language": "en-US,en;q=0.9",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36 Edg/91.0.864.41",
        }
      });

      const release = () => {
        if (promise === this._wsPromise) this._wsPromise = undefined;
      };

      ws.on('open', () => {
        resolve({ ws });
      });

      ws.on('error', (error) => {
        release();
        reject(error);
      });

      ws.on('close', () => {
        release();
      });
    });

    this._wsPromise = promise;
    return this._wsPromise;
  }

  async synthesize(text: string) {
    const { ws } = await this.getConnection();
    const date = new Date().toString();

    let download_audio = false;
    let audio_chunks = [] as Uint8Array[];
    let audio_end_callback!: () => void;

    let sentenceSubtitle = new SubMaker();
    let wordSubtitle = new SubMaker();

    ws.on('message', (data: Buffer) => {
        console.log('=== NEW MESSAGE ===');
        console.log('Message length:', data.length, 'download_audio:', download_audio);

        const u8 = new Uint8Array(data);

        // Check if this message contains "Path:audio" header (but not "Path:audio.metadata")
        const audioPathNeedle = new TextEncoder().encode('Path:audio\r\n');
        const audioHeaderIndex = indexOfUint8(u8, audioPathNeedle);

        if (audioHeaderIndex !== -1 && download_audio) {
          console.log('🎵 AUDIO MESSAGE detected at index:', audioHeaderIndex);

          const audioBodyStart = audioHeaderIndex + audioPathNeedle.length;
          const audioBody = u8.slice(audioBodyStart);
          console.log('✅ Extracted audio body, length:', audioBody.length);

          // Show first few bytes for debugging
          const firstBytes = Array.from(audioBody.slice(0, 10)).map(b => b.toString(16).padStart(2, '0')).join(' ');
          console.log('Audio data first 10 bytes (hex):', firstBytes);

          // Check if this looks like JSON metadata or actual audio data
          try {
            const bodyStr = new TextDecoder().decode(audioBody.slice(0, 50));
            if (bodyStr.includes('{') && bodyStr.includes('Metadata')) {
              console.log('📊 This is metadata, not audio data');
              // This is metadata, handle it in the metadata section below
            } else {
              console.log('🎵 This is actual audio data');
              audio_chunks.push(audioBody);
            }
          } catch (e) {
            // If we can't decode as text, it's probably binary audio data
            console.log('🎵 Binary data detected, treating as audio');
            audio_chunks.push(audioBody);
          }

          console.log('=== END MESSAGE ===\n');
          return; // Early return for audio messages
        }
        
        // Try to decode as text for control messages
        let stringData: string;
        try {
          stringData = data.toString('utf8');
          
          // Check if it looks like a text protocol message (but not audio)
          if (stringData.includes('\r\n') && stringData.includes('Path:') && !stringData.includes('Path:audio')) {
            console.log('📝 CONTROL MESSAGE detected');
            
            // Parse headers
            const headers = {} as Record<string, string>;
            const headersEnd = stringData.indexOf('\r\n\r\n');

            if (headersEnd !== -1) {
              stringData.slice(0, headersEnd).split('\r\n').forEach(line => {
                let sep = line.indexOf(':');
                if (sep !== -1) {
                  let key = line.slice(0, sep).toLowerCase();
                  let value = line.slice(sep + 1).trim();
                  headers[key] = value;
                }
              });

              const body = stringData.slice(headersEnd + 4);
              console.log('Headers:', headers);

              switch (headers.path) {
                case "turn.start": {
                  console.log('🟢 TURN START - Setting download_audio = true');
                  download_audio = true;
                  break;
                }

                case "turn.end": {
                  console.log('🔴 TURN END - Setting download_audio = false');
                  download_audio = false;
                  console.log('Total audio chunks collected:', audio_chunks.length);
                  audio_end_callback();
                  break;
                }

                case 'audio.metadata': {
                  console.log('📊 AUDIO METADATA received');
                  try {
                    const metadata = JSON.parse(body);
                    (metadata as any).Metadata.forEach((meta: any) => {
                      let sub: SubMaker | undefined;
                      if (meta.Type === 'WordBoundary') sub = wordSubtitle;
                      if (meta.Type === 'SentenceBoundary') sub = sentenceSubtitle;
                      if (sub) sub.create_sub([meta.Data.Offset, meta.Data.Duration], meta.Data.text.Text);
                    });
                  } catch (e) {
                    console.log('Failed to parse metadata JSON');
                  }
                  break;
                }

                case "response": {
                  console.log('📡 RESPONSE message received');
                  break;
                }

                default: {
                  console.log('❓ Unknown path:', headers.path);
                  break;
                }
              }
            }
          } else {
            console.log('🔤 Non-audio string data (first 100 chars):', stringData.substring(0, 100));
          }
        } catch (e) {
          console.log('❌ Failed to decode as UTF-8, probably binary non-audio data');
        }
        
        console.log('=== END MESSAGE ===\n');
    });

    const finalReturn = {
      chunks: audio_chunks,
      sentenceSubtitle,
      wordSubtitle
    };

    return new Promise<typeof finalReturn>((resolve) => {
      audio_end_callback = () => {
        console.log('Audio synthesis completed, total chunks:', audio_chunks.length);
        resolve(finalReturn);
        ws.close();
      };

      console.log('Sending speech config...');
      ws.send([
        `X-Timestamp:${date}`,
        "Content-Type:application/json; charset=utf-8",
        "Path:speech.config",
        "",
        '{"context":{"synthesis":{"audio":{"metadataoptions":{',
        '"sentenceBoundaryEnabled":true,"wordBoundaryEnabled":true},',
        '"outputFormat":"' + this.format + '"',
        "}}}}\r\n",
      ].join('\r\n'));

      console.log('Sending SSML data...');
      ws.send(ssml_headers_plus_data(
        connect_id(),
        date,
        mkssml(text, this.voice, '+0%', '+0%')
      ));
    });
  }
}

function ssml_headers_plus_data(request_id: string, timestamp: string, ssml: string) {
  return (
    `X-RequestId:${request_id}\r\n` +
    "Content-Type:application/ssml+xml\r\n" +
    `X-Timestamp:${timestamp}Z\r\n` + //  # This is not a mistake, Microsoft Edge bug.
    "Path:ssml\r\n\r\n" +
    ssml
  );
}

function connect_id() {
  return randomUUID().replace(/-/g, '');
}

function mkssml(text: string | Uint8Array, voice: string, rate: string, volume: string): string {
  text = toString(text);
  const ssml = `<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'>` +
    `<voice name='${voice}'><prosody pitch='+0Hz' rate='${rate}' volume='${volume}'>${text}</prosody></voice></speak>`;
  return ssml;
}

function toString(text: string | Uint8Array): string {
  if (typeof text === 'string') return text;
  return new TextDecoder().decode(text);
}

function indexOfUint8(data: Uint8Array, needle: Uint8Array): number {
  for (let i = 0; i < data.byteLength - needle.length; i++) {
    let j = 0;
    for (; j < needle.length && data[i + j] === needle[j]; j++);
    if (j === needle.length) return i;
  }
  return -1;
}